# Dependencies
node_modules/
.pnpm-debug.log*
packages/riva_ash/assets/node_modules/

# Session files
cookies.txt

# Backend build outputs
packages/riva_ash/_build/
packages/riva_ash/deps/
packages/riva_ash/cover/
packages/riva_ash/doc/
packages/riva_ash/hex/
packages/riva_ash/mix/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Database
packages/riva_ash/priv/static/
packages/riva_ash/priv/gettext/*/LC_MESSAGES/*.mo

# Logs
*.log
logs/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE files
.vscode/
.idea/
*.swp
*.swo

# Temporary files
*.tmp
*.temp

# Elixir specific
*.ez
packages/riva_ash/erl_crash.dump
packages/riva_ash/.elixir_ls/
packages/riva_ash/.elixir-tools/

# Mix artifacts
packages/riva_ash/mix.lock.backup

# Browser testing artifacts
packages/riva_ash/chromedriver*
packages/riva_ash/LICENSE.chromedriver
packages/riva_ash/*.sh
packages/riva_ash/debug_*.exs
packages/riva_ash/test_*.js
packages/riva_ash/test_output.txt
packages/riva_ash/test/simple_*.exs
