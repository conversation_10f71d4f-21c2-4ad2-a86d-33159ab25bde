{"name": "riva-monorepo", "version": "1.0.0", "description": "Monorepo containing Ash backend and React frontend", "private": true, "workspaces": ["frontend", "packages/*"], "scripts": {"dev": "./scripts/dev.sh", "dev:enhanced": "./scripts/dev.sh", "dev:db-only": "./scripts/dev.sh --db-only", "dev:no-setup": "./scripts/dev.sh --no-setup", "dev:with-db": "pnpm run db:start && sleep 3 && pnpm run dev:backend", "dev:concurrent": "concurrently \"pnpm run db:start\" \"pnpm run dev:wait-and-start\"", "dev:wait-and-start": "pnpm run wait:db && pnpm run dev:backend", "dev:backend": "dotenv -- pnpm run dev:backend:raw", "dev:backend:raw": "cd packages/riva_ash && mix phx.server", "dev:docker": "pnpm run docker:start && pnpm run dev:backend", "dev:local": "cd packages/riva_ash && mix phx.server", "test": "pnpm run test:backend", "test:backend": "dotenv -- pnpm run test:backend:raw", "test:backend:raw": "cd packages/riva_ash && mix test", "test:watch": "dotenv -- pnpm run test:watch:raw", "test:watch:raw": "cd packages/riva_ash && mix test.watch", "test:property": "dotenv -- pnpm run test:property:raw", "test:property:raw": "cd packages/riva_ash && MIX_ENV=test mix test --trace --include property", "test:setup": "dotenv -- pnpm run test:setup:raw", "test:setup:raw": "cd packages/riva_ash && mix ecto.reset", "setup": "pnpm run setup:backend && pnpm install", "setup:backend": "dotenv -- pnpm run setup:backend:raw", "setup:backend:raw": "cd packages/riva_ash && mix deps.get && mix ecto.setup", "deps:get": "cd packages/riva_ash && mix deps.get", "deps:update": "cd packages/riva_ash && mix deps.update --all", "deps:clean": "cd packages/riva_ash && mix deps.clean --all", "deps:compile": "cd packages/riva_ash && mix deps.compile", "compile": "cd packages/riva_ash && mix compile", "compile:force": "cd packages/riva_ash && mix compile --force", "clean": "cd packages/riva_ash && mix clean", "format": "cd packages/riva_ash && mix format", "format:check": "cd packages/riva_ash && mix format --check-formatted", "credo": "cd packages/riva_ash && mix credo", "credo:strict": "cd packages/riva_ash && mix credo --strict", "dialyzer": "cd packages/riva_ash && mix dialyzer", "docs": "cd packages/riva_ash && mix docs", "db:create": "dotenv -- pnpm run db:create:raw", "db:create:raw": "cd packages/riva_ash && mix ecto.create", "db:drop": "dotenv -- pnpm run db:drop:raw", "db:drop:raw": "cd packages/riva_ash && mix ecto.drop", "db:migrate": "dotenv -- pnpm run db:migrate:raw", "db:migrate:raw": "cd packages/riva_ash && mix ecto.migrate", "db:rollback": "dotenv -- pnpm run db:rollback:raw", "db:rollback:raw": "cd packages/riva_ash && mix ecto.rollback", "db:reset": "dotenv -- pnpm run db:reset:raw", "db:reset:raw": "cd packages/riva_ash && mix ecto.reset", "db:seed": "dotenv -- pnpm run db:seed:raw", "db:seed:raw": "cd packages/riva_ash && mix run priv/repo/seeds.exs", "db:setup": "dotenv -- pnpm run db:setup:raw", "db:setup:raw": "cd packages/riva_ash && mix ecto.setup", "routes": "cd packages/riva_ash && mix phx.routes", "iex": "dotenv -- pnpm run iex:raw", "iex:raw": "cd packages/riva_ash && iex -S mix", "console": "dotenv -- pnpm run console:raw", "console:raw": "cd packages/riva_ash && iex -S mix phx.server", "wait:db": "pnpm run wait:db:docker", "wait:db:docker": "./scripts/wait-for-db.sh", "wait:db:local": "until pg_isready -h localhost -p 5432; do echo 'Waiting for PostgreSQL...'; sleep 1; done", "db:start": "./scripts/start-postgres.sh", "db:stop": "./scripts/start-postgres.sh --stop", "db:status": "docker ps --filter name=riva-postgres-dev", "db:docker:start": "./docker-dev.sh start", "db:docker:stop": "./docker-dev.sh stop", "db:docker:reset": "./docker-dev.sh reset", "db:docker:logs": "./docker-dev.sh logs", "db:docker:status": "./docker-dev.sh status", "db:local:start": "sudo service postgresql start", "db:local:stop": "sudo service postgresql stop", "db:local:status": "sudo service postgresql status", "docker:start": "./docker-dev.sh start", "docker:stop": "./docker-dev.sh stop", "docker:reset": "./docker-dev.sh reset", "docker:logs": "./docker-dev.sh logs", "docker:status": "./docker-dev.sh status"}, "devDependencies": {"concurrently": "^8.2.2", "dotenv-cli": "^8.0.0"}, "engines": {"node": ">=18.0.0", "pnpm": ">=8.0.0"}, "packageManager": "pnpm@8.15.0", "dependencies": {"playwright": "^1.54.1"}}