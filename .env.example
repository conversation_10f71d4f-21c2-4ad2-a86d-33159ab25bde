# Database Configuration (Local Development)
# Use these values when connecting to a local PostgreSQL installation
DB_HOSTNAME=localhost
DB_PORT=5432
DB_USERNAME=postgres
DB_PASSWORD=postgres
DB_NAME=riva_ash_dev

# Phoenix Configuration
MIX_ENV=dev
PHX_HOST=localhost
PHX_PORT=4000
PORT=4000
SECRET_KEY_BASE=EuaQggrb3gfhrDAQUZqTsTMUt7zf9voCI2frB3kuyBabOCHiEue48mXJiMtL7QLj

# Development Configuration
ELIXIR_VERSION=1.18
POSTGRES_VERSION=15

# Docker Configuration
# Use these values when running with Docker Compose
# DB_HOSTNAME=postgres
# DB_PORT=5432
# MIX_ENV=prod
# PORT=4000

# Docker-specific variables (for Docker setup)
DOCKER_POSTGRES_PORT=5433
DOCKER_POSTGRES_USER=postgres
DOCKER_POSTGRES_PASSWORD=postgres
DOCKER_POSTGRES_DB=riva_ash_dev

# Production Database URL (for production deployment)
# DATABASE_URL=ecto://username:password@hostname:port/database

# IMPORTANT SECURITY NOTES:
# 1. The default passwords in this file are for development only
# 2. Always change default passwords in production environments
# 3. Never commit actual passwords or secrets to version control
# 4. Use strong, unique passwords for production databases
# 5. Consider using environment-specific .env files for different environments

# When to use which configuration:
# - Local Development: Use the default values at the top (DB_HOSTNAME=localhost, etc.)
# - Docker Development: Uncomment the Docker values and comment out the local ones
# - Production: Use DATABASE_URL and ensure all secrets are properly secured
