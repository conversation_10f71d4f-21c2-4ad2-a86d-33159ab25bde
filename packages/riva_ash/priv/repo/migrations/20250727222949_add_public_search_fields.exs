defmodule RivaAsh.Repo.Migrations.AddPublicSearchFields do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:businesses) do
      add(:is_public_searchable, :boolean, null: false, default: false)
      add(:public_description, :text)
    end

    create table(:consent_records_versions, primary_key: false) do
      add(:id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true)
      add(:version_action_type, :text, null: false)
      add(:version_action_name, :text, null: false)
      add(:version_action_inputs, :map, null: false)
      add(:version_resource_identifier, :text, null: false)
      add(:version_source_id, :uuid, null: false)
      add(:changes, :map)

      add(:version_inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(:version_updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )
    end

    alter table(:items) do
      add(:is_public_searchable, :boolean, null: false, default: false)
      add(:public_description, :text)
    end

    create table(:consent_records, primary_key: false) do
      add(:id, :uuid, null: false, default: fragment("gen_random_uuid()"), primary_key: true)
    end

    alter table(:consent_records_versions) do
      modify(
        :version_source_id,
        references(:consent_records,
          column: :id,
          name: "consent_records_versions_version_source_id_fkey",
          type: :uuid,
          prefix: "public"
        )
      )
    end

    alter table(:consent_records) do
      add(
        :user_id,
        references(:users,
          column: :id,
          name: "consent_records_user_id_fkey",
          type: :uuid,
          prefix: "public"
        ), null: false)

      add(
        :business_id,
        references(:businesses,
          column: :id,
          name: "consent_records_business_id_fkey",
          type: :uuid,
          prefix: "public"
        )
      )

      add(:purpose, :text, null: false)
      add(:consent_given, :boolean, null: false)
      add(:consent_date, :utc_datetime, null: false)
      add(:withdrawal_date, :utc_datetime)
      add(:consent_version, :text, null: false)
      add(:ip_address, :text)
      add(:user_agent, :text)
      add(:consent_method, :text, null: false, default: "web_form")

      add(:inserted_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )

      add(:updated_at, :utc_datetime_usec,
        null: false,
        default: fragment("(now() AT TIME ZONE 'utc')")
      )
    end

    create(
      unique_index(:consent_records, [:user_id, :purpose, :consent_date],
        name: "consent_records_unique_user_purpose_date_index"
      )
    )
  end

  def down do
    drop_if_exists(
      unique_index(:consent_records, [:user_id, :purpose, :consent_date],
        name: "consent_records_unique_user_purpose_date_index"
      )
    )

    drop(constraint(:consent_records, "consent_records_user_id_fkey"))

    drop(constraint(:consent_records, "consent_records_business_id_fkey"))

    alter table(:consent_records) do
      remove(:updated_at)
      remove(:inserted_at)
      remove(:consent_method)
      remove(:user_agent)
      remove(:ip_address)
      remove(:consent_version)
      remove(:withdrawal_date)
      remove(:consent_date)
      remove(:consent_given)
      remove(:purpose)
      remove(:business_id)
      remove(:user_id)
    end

    drop(constraint(:consent_records_versions, "consent_records_versions_version_source_id_fkey"))

    alter table(:consent_records_versions) do
      modify(:version_source_id, :uuid)
    end

    drop(table(:consent_records))

    alter table(:items) do
      remove(:public_description)
      remove(:is_public_searchable)
    end

    drop(table(:consent_records_versions))

    alter table(:businesses) do
      remove(:public_description)
      remove(:is_public_searchable)
    end
  end
end
