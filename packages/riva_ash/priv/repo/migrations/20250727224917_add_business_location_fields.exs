defmodule RivaAsh.Repo.Migrations.AddBusinessLocationFields do
  @moduledoc """
  Updates resources based on their most recent snapshots.

  This file was autogenerated with `mix ash_postgres.generate_migrations`
  """

  use Ecto.Migration

  def up do
    alter table(:businesses) do
      add(:city, :text)
      add(:country, :text)
      add(:address, :text)
    end
  end

  def down do
    alter table(:businesses) do
      remove(:address)
      remove(:country)
      remove(:city)
    end
  end
end
