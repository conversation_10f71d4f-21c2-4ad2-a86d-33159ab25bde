@import "./salad_ui.css";

@tailwind base;
@tailwind components;
@tailwind utilities;


@layer base {
  :root {
    /* Improved color palette based on UX research */
    --background: oklch(0.99 0.002 260); /* Slightly cool white */
    --foreground: oklch(0.17 0.005 260); /* Soft near-black */
    --card: oklch(0.98 0.002 260);
    --card-foreground: oklch(0.20 0.006 260);
    --popover: oklch(0.97 0.003 260);
    --popover-foreground: oklch(0.18 0.005 260);

    /* Primary - Professional indigo (trust/concentration) */
    --primary: oklch(0.52 0.18 275);
    --primary-foreground: oklch(0.98 0.001 260);

    /* Secondary - Calm teal (balance) */
    --secondary: oklch(0.65 0.10 185);
    --secondary-foreground: oklch(0.15 0.008 260);

    /* Accent - Accessible amber (attention) */
    --accent: oklch(0.70 0.22 75);
    --accent-foreground: oklch(0.15 0.008 260);

    /* Destructive - Research-backed soft red */
    --destructive: oklch(0.55 0.20 25);
    --destructive-foreground: oklch(0.98 0.001 260);

    /* Improved neutrals */
    --muted: oklch(0.92 0.003 260);
    --muted-foreground: oklch(0.45 0.008 260);
    --border: oklch(0.85 0.005 260);
    --input: oklch(0.90 0.004 260);
    --ring: oklch(0.52 0.18 275);

    /* WCAG-optimized chart colors */
    --chart-1: oklch(0.52 0.18 275); /* Primary indigo */
    --chart-2: oklch(0.65 0.10 185); /* Teal */
    --chart-3: oklch(0.70 0.22 75);  /* Amber */
    --chart-4: oklch(0.55 0.12 330); /* Accessible magenta */
    --chart-5: oklch(0.50 0.16 150); /* Balanced green */

    /* Sidebar - Subtle differentiation */
    --sidebar-background: oklch(0.96 0.002 260);
    --sidebar-foreground: oklch(0.25 0.006 260);
    --sidebar-primary: oklch(0.52 0.18 275);
    --sidebar-primary-foreground: oklch(0.98 0.001 260);
    --sidebar-accent: oklch(0.70 0.22 75);
    --sidebar-accent-foreground: oklch(0.15 0.008 260);
    --sidebar-border: oklch(0.88 0.004 260);
    --sidebar-ring: oklch(0.52 0.18 275);

    /* Typography */
    --font-sans: "Inter", system-ui, sans-serif; /* Better readability */
    --font-serif: Lora, ui-serif, serif; /* More modern serif */
    --font-mono: "IBM Plex Mono", monospace; /* Better coding font */

    /* Interaction improvements */
    --radius: 6px; /* Softened corners */
    --shadow-sm: 0 2px 4px oklch(0 0 0 / 0.08); /* Subtle depth */
    --shadow-md: 0 4px 8px oklch(0 0 0 / 0.12);
    --shadow-lg: 0 8px 16px oklch(0 0 0 / 0.16);
    --spacing: 0.3rem; /* Improved rhythm */
}

.dark {
    --background: oklch(0.14 0.004 260); /* Dark gray (not pure black) */
    --foreground: oklch(0.92 0.002 260);
    --card: oklch(0.16 0.005 260);
    --card-foreground: oklch(0.90 0.002 260);
    --popover: oklch(0.18 0.005 260);
    --popover-foreground: oklch(0.90 0.002 260);

    /* Dark mode color adjustments */
    --primary: oklch(0.60 0.16 275); /* Lighter indigo */
    --primary-foreground: oklch(0.98 0.001 260);
    --secondary: oklch(0.70 0.09 185); /* Brighter teal */
    --secondary-foreground: oklch(0.15 0.008 260);
    --accent: oklch(0.75 0.20 75);   /* Vibrant amber */
    --accent-foreground: oklch(0.15 0.008 260);
    --destructive: oklch(0.65 0.18 25); /* Softer red */
    --destructive-foreground: oklch(0.98 0.001 260);

    --muted: oklch(0.20 0.006 260);
    --muted-foreground: oklch(0.70 0.008 260);
    --border: oklch(0.25 0.008 260);
    --input: oklch(0.22 0.007 260);
    --ring: oklch(0.60 0.16 275);

    /* Chart color adjustments for dark mode */
    --chart-1: oklch(0.60 0.16 275);
    --chart-2: oklch(0.70 0.09 185);
    --chart-3: oklch(0.75 0.20 75);
    --chart-4: oklch(0.65 0.14 330);
    --chart-5: oklch(0.65 0.14 150);

    /* Sidebar dark mode */
    --sidebar-background: oklch(0.16 0.005 260);
    --sidebar-foreground: oklch(0.90 0.002 260);
    --sidebar-primary: oklch(0.60 0.16 275);
    --sidebar-primary-foreground: oklch(0.98 0.001 260);
    --sidebar-accent: oklch(0.75 0.20 75);
    --sidebar-accent-foreground: oklch(0.15 0.008 260);
    --sidebar-border: oklch(0.28 0.008 260);
    --sidebar-ring: oklch(0.60 0.16 275);
}

  * {
    @apply border-border !important;
  }
}




