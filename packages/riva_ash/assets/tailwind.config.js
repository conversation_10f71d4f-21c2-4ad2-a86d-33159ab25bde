/** @type {import('tailwindcss').Config} */
module.exports = {
  important: ".riva-ash-web",
  content: [
    "../deps/salad_ui/lib/**/*.ex",
    "../lib/**/*.{ex,heex,eex}",
    "../lib/**/*.{js,jsx,ts,tsx}",
    "./js/**/*.{js,jsx,ts,tsx}",
    "../priv/static/**/*.{js,css}",
    "../storybook/**/*.exs"
  ],
  theme: {
    extend: {
      colors: require("./tailwind.colors.json"),
      fontFamily: {
        sans: ["var(--font-sans)", "Inter", "system-ui", "sans-serif"],
        serif: ["var(--font-serif)", "Lora", "ui-serif", "serif"],
        mono: ["var(--font-mono)", "IBM Plex Mono", "monospace"],
      },
      borderRadius: {
        DEFAULT: "var(--radius)",
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      boxShadow: {
        sm: "var(--shadow-sm)",
        DEFAULT: "var(--shadow-md)",
        md: "var(--shadow-md)",
        lg: "var(--shadow-lg)",
      },
      spacing: {
        rhythm: "var(--spacing)",
      },
    },
  },
  plugins: [],
}
