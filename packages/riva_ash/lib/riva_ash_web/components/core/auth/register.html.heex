<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
  <div class="max-w-md w-full space-y-8">
    <div>
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
        Create your account
      </h2>
      <p class="mt-2 text-center text-sm text-gray-600">
        Join <PERSON><PERSON> to manage your business reservations
      </p>
    </div>

    <!-- Display flash messages -->
    <%= if Phoenix.Flash.get(@flash, :error) do %>
      <div class="rounded-md bg-red-50 p-4">
        <div class="flex">
          <div class="flex-shrink-0">
            <svg class="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
            </svg>
          </div>
          <div class="ml-3">
            <h3 class="text-sm font-medium text-red-800">Registration Error</h3>
            <div class="mt-2 text-sm text-red-700">
              <%= Phoenix.Flash.get(@flash, :error) %>
            </div>
          </div>
        </div>
      </div>
    <% end %>

    <form class="mt-8 space-y-6" action="/register" method="post" id="registration-form">
      <input type="hidden" name="_csrf_token" value={get_csrf_token()} />
      <div class="space-y-4">
        <div>
          <label for="name" class="block text-sm font-medium text-gray-700">Full name</label>
          <input
            id="name"
            name="name"
            type="text"
            autocomplete="name"
            required
            minlength="2"
            maxlength="100"
            class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
            placeholder="Enter your full name"
          />
          <p class="mt-1 text-xs text-gray-500">Your full name (2-100 characters)</p>
        </div>

        <div>
          <label for="email" class="block text-sm font-medium text-gray-700">Email address</label>
          <input
            id="email"
            name="email"
            type="email"
            autocomplete="email"
            required
            class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
            placeholder="Enter your email address"
          />
          <p class="mt-1 text-xs text-gray-500">We'll use this for your account login</p>
        </div>

        <div>
          <label for="password" class="block text-sm font-medium text-gray-700">Password</label>
          <input
            id="password"
            name="password"
            type="password"
            autocomplete="new-password"
            required
            minlength="8"
            class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
            placeholder="Create a secure password"
          />
          <p class="mt-1 text-xs text-gray-500">Minimum 8 characters</p>
        </div>

        <div>
          <label for="password_confirmation" class="block text-sm font-medium text-gray-700">Confirm Password</label>
          <input
            id="password_confirmation"
            name="password_confirmation"
            type="password"
            autocomplete="new-password"
            required
            minlength="8"
            class="mt-1 appearance-none relative block w-full px-3 py-2 border border-gray-300 placeholder-gray-500 text-gray-900 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 focus:z-10 sm:text-sm"
            placeholder="Confirm your password"
          />
          <p class="mt-1 text-xs text-gray-500">Must match your password</p>
          <div id="password-match-error" class="mt-1 text-xs text-red-600 hidden">
            Passwords do not match
          </div>
        </div>
      </div>

      <div>
        <button
          type="submit"
          id="submit-button"
          class="group relative w-full flex justify-center py-2 px-4 border border-transparent text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <span id="submit-text">Create Account</span>
          <span id="submit-loading" class="hidden">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
              <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Creating account...
          </span>
        </button>
      </div>

      <div class="text-center">
        <a href="/sign-in" class="font-medium text-indigo-600 hover:text-indigo-500">
          Already have an account? Sign in here
        </a>
      </div>
    </form>
  </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
  const form = document.getElementById('registration-form');
  const passwordField = document.getElementById('password');
  const confirmPasswordField = document.getElementById('password_confirmation');
  const passwordMatchError = document.getElementById('password-match-error');
  const submitButton = document.getElementById('submit-button');
  const submitText = document.getElementById('submit-text');
  const submitLoading = document.getElementById('submit-loading');

  // Real-time password confirmation validation
  function validatePasswordMatch() {
    const password = passwordField.value;
    const confirmPassword = confirmPasswordField.value;

    if (confirmPassword.length > 0) {
      if (password !== confirmPassword) {
        passwordMatchError.classList.remove('hidden');
        confirmPasswordField.classList.add('border-red-300');
        confirmPasswordField.classList.remove('border-gray-300');
        return false;
      } else {
        passwordMatchError.classList.add('hidden');
        confirmPasswordField.classList.remove('border-red-300');
        confirmPasswordField.classList.add('border-gray-300');
        return true;
      }
    }
    return true;
  }

  // Add event listeners for real-time validation
  confirmPasswordField.addEventListener('input', validatePasswordMatch);
  passwordField.addEventListener('input', validatePasswordMatch);

  // Form submission handling
  form.addEventListener('submit', function(e) {
    // Validate password match before submission
    if (!validatePasswordMatch()) {
      e.preventDefault();
      return false;
    }

    // Show loading state
    submitButton.disabled = true;
    submitText.classList.add('hidden');
    submitLoading.classList.remove('hidden');
  });

  // Email validation
  const emailField = document.getElementById('email');
  emailField.addEventListener('blur', function() {
    const email = emailField.value;
    const emailRegex = /^[^\s]+@[^\s]+\.[^\s]+$/;

    if (email && !emailRegex.test(email)) {
      emailField.classList.add('border-red-300');
      emailField.classList.remove('border-gray-300');
    } else {
      emailField.classList.remove('border-red-300');
      emailField.classList.add('border-gray-300');
    }
  });

  // Name validation
  const nameField = document.getElementById('name');
  nameField.addEventListener('blur', function() {
    const name = nameField.value.trim();

    if (name.length < 2 || name.length > 100) {
      nameField.classList.add('border-red-300');
      nameField.classList.remove('border-gray-300');
    } else {
      nameField.classList.remove('border-red-300');
      nameField.classList.add('border-gray-300');
    }
  });
});
</script>
