<!DOCTYPE html>
<html lang="en" class="[scrollbar-gutter:stable]">
  <head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta name="csrf-token" content={get_csrf_token()} />
    <title><%= assigns[:page_title] || "RivaAsh" %></title>
    <link phx-track-static rel="stylesheet" href={~p"/assets/app.css"} />
    <script defer phx-track-static type="text/javascript" src={~p"/assets/app.js"}>
    </script>
  </head>
  <body class="bg-white antialiased riva-ash-web">
    <div class="flex flex-col min-h-screen">
      <!-- Top Navigation Bar -->
      <header class="bg-blue-600 shadow-sm border-gray-200 border-b">
      <div class="px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Left side - Logo and breadcrumb -->
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <h1 class="font-semibold text-white text-xl">Riva</h1>
            </div>
            <!-- Breadcrumb could go here -->
            <nav class="hidden md:flex ml-8" aria-label="Breadcrumb">
              <ol class="flex items-center space-x-4">
                <li>
                  <div class="flex items-center">
                    <svg class="flex-shrink-0 w-5 h-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 20 20" aria-hidden="true">
                      <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z"/>
                    </svg>
                    <.link navigate="/dashboard" class="ml-2 font-medium text-gray-500 hover:text-gray-700 text-sm">
                      Dashboard
                    </.link>
                  </div>
                </li>
              </ol>
            </nav>
          </div>

        <!-- Right side - User info and actions -->
        <div class="flex items-center space-x-4">
          <!-- Notifications -->
          <button type="button" class="bg-white p-1 rounded-full focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 text-gray-400 hover:text-gray-500">
            <span class="sr-only">View notifications</span>
            <svg class="w-6 h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-5 5-5-5h5V3h0z"/>
            </svg>
          </button>

          <div :if={@current_user}>
            <!-- User dropdown -->
            <div class="relative">
              <div class="flex items-center space-x-3">
                <!-- User info -->
                <div class="hidden md:flex flex-col items-end">
                  <div class="font-medium text-gray-900 text-sm">
                    <%= @current_user.name || @current_user.email %>
                  </div>
                  <div class="text-gray-500 text-xs">
                    <%= String.capitalize(to_string(@current_user.role)) %>
                  </div>
                </div>

                <!-- User avatar -->
                <div class="flex-shrink-0">
                  <div class="flex justify-center items-center bg-indigo-500 rounded-full w-8 h-8">
                    <span class="font-medium text-white text-sm">
                      <%= String.first(@current_user.name || @current_user.email) |> String.upcase() %>
                    </span>
                  </div>
                </div>
              </div>
            </div>

            <!-- Sign out button -->
            <form action="/sign-out" method="post">
              <input type="hidden" name="_csrf_token" value={get_csrf_token()}>
              <button type="submit" class="bg-white p-2 rounded-md focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 text-gray-400 hover:text-gray-500">
                <span class="sr-only">Sign out</span>
                <svg class="w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
              </button>
            </form>
          </div>
        </div>
      </div>
    </div>
  </header>

  <!-- Side Navigation and Content -->
  <div class="flex flex-1">
    <!-- Side Navigation -->
    <aside class="hidden md:block bg-gray-50 shadow-sm border-gray-200 border-r w-64">
      <nav class="space-y-6 p-4">
        <!-- Dashboard Hub -->
        <div>
          <.link
            navigate={~p"/dashboard"}
            class="flex items-center hover:bg-gray-100 px-3 py-2 rounded-lg text-gray-700 hover:text-gray-900"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="mr-3 w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
            </svg>
            Dashboard Hub
          </.link>
        </div>

        <!-- Core Workflows -->
        <div>
          <h3 class="px-3 font-semibold text-gray-500 text-xs uppercase tracking-wider">Core Workflows</h3>
          <div class="space-y-1 mt-2">
            <.link
              navigate={~p"/setup"}
              class="flex items-center hover:bg-gray-100 px-3 py-2 rounded-lg text-gray-700 hover:text-gray-900"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="mr-3 w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
              🏗️ Business Setup
            </.link>

            <.link
              navigate={~p"/reservations"}
              class="flex items-center hover:bg-gray-100 px-3 py-2 rounded-lg text-gray-700 hover:text-gray-900"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="mr-3 w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
              </svg>
              📅 Reservation Center
            </.link>

            <.link
              navigate={~p"/inventory"}
              class="flex items-center hover:bg-gray-100 px-3 py-2 rounded-lg text-gray-700 hover:text-gray-900"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="mr-3 w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
              </svg>
              📦 Inventory Management
            </.link>

            <.link
              navigate={~p"/people"}
              class="flex items-center hover:bg-gray-100 px-3 py-2 rounded-lg text-gray-700 hover:text-gray-900"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="mr-3 w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 515.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 919.288 0M15 7a3 3 0 11-6 0 3 3 0 616 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              👥 People Management
            </.link>

            <.link
              navigate={~p"/finance"}
              class="flex items-center hover:bg-gray-100 px-3 py-2 rounded-lg text-gray-700 hover:text-gray-900"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="mr-3 w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              💰 Financial Operations
            </.link>
          </div>
        </div>

        <!-- System Administration -->
        <div>
          <h3 class="px-3 font-semibold text-gray-500 text-xs uppercase tracking-wider">System</h3>
          <div class="space-y-1 mt-2">
            <.link
              navigate={~p"/settings"}
              class="flex items-center hover:bg-gray-100 px-3 py-2 rounded-lg text-gray-700 hover:text-gray-900"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="mr-3 w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              </svg>
              ⚙️ Settings & Config
            </.link>
          </div>
        </div>

        <!-- Administration -->
        <%= if @current_user && @current_user.role == :admin do %>
          <div>
            <h3 class="px-3 font-semibold text-gray-500 text-xs uppercase tracking-wider">Administration</h3>
            <div class="space-y-1 mt-2">
              <.link
                href="/admin"
                class="flex items-center hover:bg-gray-100 px-3 py-2 rounded-lg text-gray-700 hover:text-gray-900"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="mr-3 w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 616 0z" />
                </svg>
                Admin Panel
              </.link>

              <.link
                href="/erd"
                class="flex items-center hover:bg-gray-100 px-3 py-2 rounded-lg text-gray-700 hover:text-gray-900"
              >
                <svg xmlns="http://www.w3.org/2000/svg" class="mr-3 w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 712-2h2a2 2 0 712 2" />
                </svg>
                Entity Diagram
              </.link>
            </div>
          </div>
        <% end %>
      </nav>
    </aside>

      <!-- Main Content -->
      <main class="flex-1 bg-white overflow-y-auto">
        <div class="mx-auto p-4 container">
          <.flash_group flash={@flash} />
          <%= @inner_content %>
        </div>
      </main>
    </div>
    </div>
  </body>
</html>
