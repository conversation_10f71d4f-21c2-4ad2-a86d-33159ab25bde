defmodule RivaAshWeb.AshAdminConfig do
  @moduledoc """
  Configuration for AshAdmin to handle authentication and actor setup.
  Provides superadmin access control for the admin interface.
  """

  def actor(conn) do
    # Get the current user from the connection assigns
    # This will be set by the :require_authenticated_user plug
    conn.assigns[:current_user]
  end

  def set_actor(conn) do
    # TODO: Update to use the correct Ash function for setting actor
    # The Ash.set_actor!/2 function appears to be deprecated
    case actor(conn) do
      nil ->
        # Ash.set_actor!(conn, nil)
        conn
      _user ->
        # Ash.set_actor!(conn, user)
        conn
    end
  end

  @doc """
  Authorization function for AshAdmin access.
  Only allows superadmin users to access the admin interface.
  This provides GDPR-compliant oversight capabilities.
  """
  def authorize(conn) do
    case actor(conn) do
      %{role: :superadmin} ->
        :ok
      %{role: :admin} ->
        # Regular admins can access admin interface but with limited scope
        :ok
      _ ->
        {:error, :forbidden}
    end
  end

  @doc """
  Check if the current user is a superadmin.
  Used for conditional UI elements and additional access controls.
  """
  def is_superadmin?(conn) do
    case actor(conn) do
      %{role: :superadmin} -> true
      _ -> false
    end
  end

  @doc """
  Get user role for display purposes in admin interface.
  """
  def user_role(conn) do
    case actor(conn) do
      %{role: role} -> role
      _ -> :anonymous
    end
  end
end
